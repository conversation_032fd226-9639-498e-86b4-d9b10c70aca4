@charset "utf-8";
/* css for product center */
a.buy1,a.buy2,a.buy3,a.free1,a.free2,a.free3{
	background:url(../images/common/btn_icon.gif) no-repeat;
	color:#fff;
	display:block;
	text-decoration:none;
	float:left;
	font-size:14px;
}
a.buy1{
	background-position:0 top;
	width:84px;
	padding-left:35px;
	line-height:28px;
}
a.buy1:hover{
	background-position:0 -28px;
	text-decoration:none;
}
a.free1{
	background-position:0 -56px;
	width:84px;
	padding-left:35px;
	line-height:28px;
}
a.free1:hover{
	background-position:0 -84px;
	text-decoration:none;
}
a.mac{
	background:url(../images/common/mac_btn.gif) no-repeat;
	display:block;
	width:116px;
	height:25px;
	line-height:25px;
	color:#A40000;
	padding-left:34px;
	float:right;
	font-weight:bold;
}
a.win{
	background:url(../images/common/win_btn.gif) no-repeat;
	display:block;
	width:136px;
	height:25px;
	line-height:25px;
	color:#A40000;
	padding-left:34px;
	float:right;
	font-weight:bold;
}
a.win:hover{text-decoration:none}
.product_center { width:700px;}
.product_center .item_tab { width:100%; overflow:hidden; }
.product_center .item_tab li { float:left; margin-right:8px; padding-left:45px;}
.product_center .item_tab li a{display:block !important;display:inline-block;color:#666; font-weight:bold; font-size:13px; height:21px; padding:12px 25px 0 0; outline:none; }
.product_center .item_tab li#mac { background:url(../images/common/pro_list_item_tab.png) left -34px; }
.product_center .item_tab li#mac a {background:url(../images/common/pro_list_item_tab.png) right -34px; }
.product_center .item_tab li#mac.current { background:url(../images/common/pro_list_item_tab.png) left 0; }
.product_center .item_tab li#mac.current a { color:#fff; background:url(../images/common/pro_list_item_tab.png) right 0; }
.product_center .item_tab li#mac.current a:hover { color:#F28500; }
.product_center .item_tab li#win { background:url(../images/common/pro_list_item_tab.png) left -102px; }
.product_center .item_tab li#win a {background:url(../images/common/pro_list_item_tab.png) right -102px; }
.product_center .item_tab li#win.current { background:url(../images/common/pro_list_item_tab.png) left -68px; }
.product_center .item_tab li#win.current a { color:#fff; background:url(../images/common/pro_list_item_tab.png) right -68px; }
.product_center .item_tab li#win.current a:hover { color:#F28500; }
.content-p {margin:0 0 0 0px; text-align:left; float:left; }
.product_center .item_sub_menu { padding:3px 0 3px 10px; background:#2f579d; margin-bottom:10px;width:670px; }
.product_center .item_sub_menu ul { width:100%; overflow:hidden; }
.product_center .item_sub_menu ul li { float:left; color:#c4c6ca; }
.product_center .item_sub_menu ul li a{ color:#fff; text-decoration:underline; padding:0 7px; line-height:25px; outline:none; }
.product_center .item_sub_menu ul li.current a { text-decoration:none; font-weight:bold; }
.product_center .item_sub_menu ul li a:hover { text-decoration:none; }
.product_center .top_pro_info_t { width:664px; margin:0 0; height:10px; position:relative;}
.product_center .top_pro_info_b { width:664px; margin:0px 0 0px 0; height:10px; position:relative;}
.product_center .top_pro_info { width:664px; position:relative; margin:0 0; min-height:227px; background:url(../images/common/top_pro_box_m.jpg) no-repeat; }
.product_center .top_pro_info div { width:100%; overflow:hidden;}
.product_center .top_pro_info div dl dt{ margin:10px 0 0 0px;}
.product_center .top_pro_info div dl dd { width:410px; margin:15px 20px 20px 10px;}
.product_center .top_pro_info dl {}
.product_center .top_pro_info dl dt { float:left; width:180px; text-align:center; font-size:13px; color:#ff6500; font-weight:bold; margin:15px 0 0 -20px; display:inline; }
.product_center .top_pro_info dl dt del{color:#333;font-weight:normal;font-size:11px;}
.product_center .top_pro_info dl dd { float:right; width:430px; margin:20px 0px 0px 0; display:inline; }
.product_center .top_pro_info dl dd h2 a { font-size:19px; color:#333; font-weight:normal; font-family:Tahoma, Geneva, sans-serif; text-decoration:none; margin:0px 0 0 -70px;}
.product_center .top_pro_info dl dd h2 a:hover { color:#666; }
.product_center .top_pro_info dl dd .features { padding:10px 0 0px 0px; width:600px; background:none; border:none; margin:0 0 0 -70px; box-shadow:none;}
.product_center .top_pro_info dl dd .features li { padding:2px 0; }
.product_center .top_pro_info dl dd .btn { width:100%; overflow:hidden; padding:10px 0 0 0; }
.product_center .top_pro_info dl dd .btn .white{color:#FFFFFF;}
.product_center .pro_info { width:710px; margin:0 0; overflow:hidden; padding-bottom:0px; display:block; }
.product_center .pro_info dt { float:left; width:110px; text-align:center; color:#ff6500; font-weight:bold; margin:20px 0 0 10px; padding:0 0;  }
.product_center .pro_info dd { float:right; width:540px; margin:20px 20px 0 0;}
.product_center .pro_info dd h2 { font-size:13px; margin:0 0 15px 0; }
.product_center .pro_info dd h2 a{ text-decoration:none; }
.product_center .pro_info dd p { font-size:11px; line-height:1.5em; color:#555; }
.product_center .pro_info dd p a { color:#FFFFFF; margin-left:10px; }
.product_center .line { width:664px; margin:0 auto; border-bottom:dashed 1px #ccc; padding-top:10px; height:1px; }
.product_center .pro_info dd .btn { width:100%; padding:15px 0 10px 0; overflow:hidden; }
.product_center .pro_info dd .btn .white {color:#FFFFFF;}
.product_center .pro_info dd .btn .white a:hover {color: #FFFFFF;}
.product_center .pro_info dd .btn li{ float:left; line-height:26px; margin-right:30px;font-weight:bold;color:#666;}
.product_center .pro_info dd .btn li img { margin:0 7px -5px 0; }
.product_center .pro_info dd .btn li a {text-decoration:underline; color:#FFFFFF;}
.product_center .pro_info dd .btn li a:hover { text-decoration:none; color:#FFFFFF; }
#tab { padding:5px 0 3px 10px; background:#2f579d; height:20px; margin-bottom:5px; width:670px;}
#tab ul { width:100%; overflow:hidden; }
#tab ul li { float:left; color:#fff; padding:0 20px; font-size:12px;}
#tab ul li:hover { text-decoration:underline; cursor:pointer;}
#tab ul li.current { font-weight:bold; font-size:14px;}
.pro-minor {  width:555px; margin:0 0 0 140px; }
.pro-minor h3 {font-size:14px; margin:0 0 0 17px; font-weight:bold;}
.pro-minor ul li { background:url(../images/common/pro-minor-li.jpg) no-repeat 0 center; padding-left:15px; margin-left:20px; line-height:25px; }
.pro-minor ul li .pro-more { text-decoration:underline; }
.main_pro{padding:10px 0 20px 0px; width:690px;border-bottom:dashed 1px #ccc;}
.main_pro .img{float:left;width:100px;padding-top:15px;}
.main_pro .info{float:left;width:370px;padding-top:10px;}
.main_pro .info p{line-height:1.4em; margin:0 0 0 -17px;}
.main_pro .name{padding:0 0 5px;font-size:14px; color:#3e4e57; line-height:16px;}
.main_pro .name span{font-size:11px;line-height:14px;display:block;font-weight:normal;}
.main_pro .name a{font-size:16px;line-height:16px;color:#3e4e57;text-decoration:none;}
.main_pro .name a:hover{text-decoration:underline;color:#3e4e57;}
.main_pro .price{padding:40px 0 0;font-style:italic;float:left;width:80px;text-align:center;}
.main_pro .price del{padding-right:5px;color:#3e4e57;font-size:13px;display:block;}
.main_pro .price strong{color:#f60;font-size:18px;}
.main_pro .btn-buy{margin:22px 0 0 0;}
.main_pro .btn-buy img{padding-top:8px;}
.sideNav{ border:1px solid #ddd; -webkit-border-radius:5px;-moz-border-radius:5px;border-radius:5px;-webkit-box-shadow:0 2px 2px #ddd;-moz-box-shadow:0 2px 2px #ddd; box-shadow:0 2px 2px #ddd; margin-bottom:18px; overflow:hidden;}
.sideNav .name{ background:#807e7c;font-size:14px; 
color:#fff; padding:8px 0 8px 12px;}
.sideNav ul{padding:10px 0 0 0;}
.sideNav h3{ font-weight:bold; height:25px; line-height:25px; background:#f0f0f0; margin:3px 0; padding-left:15px; font-size:12px;}
.sideNav li{ margin:0 7px; }
.sideNav li a{ display:block;padding-left:10px;height:28px; line-height:28px; color:#333; }
.sideNav li a:hover{ text-decoration:none;}
.sideNav li a.curr{ text-decoration:none; color:#fff; background:url(../images/common/side-nav-h.png) no-repeat 100% 0 #e68528; }