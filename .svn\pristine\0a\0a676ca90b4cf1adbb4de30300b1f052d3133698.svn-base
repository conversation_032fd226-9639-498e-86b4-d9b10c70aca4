<!DOCTYPE html>
<html lang="en">

<head>
	<title>How to Reset Screen Time Passcode with/without Apple ID</title>
	<meta name="Keywords" content="how to reset screen time passcode without Apple ID" />
	<meta name="Description"
		content="Forgot your Screen Time Passcode? You don't have to erase your device. Here's how to reset Screen Time passcode with or without Apple ID on iOS 12/13/14/15." />
	<meta name="copyright" content="iSumsoft" />
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=1" />
	<meta name="format-detection" content="telephone=no" />
	<meta name="apple-mobile-web-app-capable" content="yes" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />

	<link rel="preload" type="font/woff" as="font" href="/fonts/bootstrap-icons.woff" crossorigin />
	<link href="../css/style.css" rel="stylesheet" type="text/css" media="screen" />
	<link rel="stylesheet" href="../css/web-app.css" media="(max-width:1024px)" /><!--web-app-style-->

</head>

<body><!-- #BeginLibraryItem "/library/header.lbi" -->
<div class="header">
    <div class="container">
        <div class="row no-gutters">
            <div class="logo"><a href="https://www.isumsoft.com" title="iSumsoft"><img width="239" height="41" src="https://www.isumsoft.com/images/common/isumsoft.png" alt="iSumsoft Studio" /></a></div>
            <div class="nav-app"><span></span></div>
            <ul class="nav">
                <li>Password Recovery<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos p">
                		<div class="hidden">
	                    <div class="row">
	                        <div class="col-12 col-md-4">
                                <a href="https://www.isumsoft.com/password-refixer-bundle/"><i class="a"></i>Password Refixer Bundle</a>
                                <a href="https://www.isumsoft.com/windows-password-refixer/"><i class="b"></i>Windows Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/windows-7-password-refixer/"><i class="c"></i>Windows 7 Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/office-password-refixer/"><i class="d"></i>Office Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/word-password-refixer/"><i class="e"></i>Word Password Refixer</a>
	                        	
	                        	
	                        </div>
	                        <div class="col-12 col-md-4">
                                <a href="https://www.isumsoft.com/excel-password-refixer/"><i class="f"></i>Excel Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/powerpoint-password-refixer/"><i class="g"></i>PowerPoint Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/rar-password-refixer/"><i class="h"></i>RAR Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/zip-password-refixer/"><i class="i"></i>ZIP Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/pdf-password-refixer/"><i class="j"></i>PDF Password Refixer</a>
	                        	
	                        </div>
                            <div class="col-12 col-md-4">
                                <a href="https://www.isumsoft.com/access-password-refixer/"><i class="k"></i>Access Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/outlook-password-refixer/"><i class="l"></i>Outlook Password Refixer</a>
                                <a href="https://www.isumsoft.com/sql-password-refixer/"><i class="m"></i>SQL Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/wifi-password-refixer/"><i class="n"></i>WiFi Password Refixer</a>
                                <a href="https://www.isumsoft.com/itunes-password-refixer/"><i class="o"></i>iTunes Password Refixer</a>
                            </div>
	                    </div>
	                  </div>
                  </div> 
                </li>
                <li>Backup & Cleanup<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos c">
                		<div class="hidden">
                            <a href="https://www.isumsoft.com/cloner/">
                                <img loading="lazy" src="../images/icon/cloner-38.png" width="38" height="38" alt="cloner" />
                                <span>
                                    <b>Cloner</b>
                                    <em>Disk Cloning</em>
                                </span>
                            </a>
                            <a href="https://www.isumsoft.com/filezero/">
                                <img loading="lazy" src="../images/icon/filezero-38.png" width="38" height="38" alt="filezero" />
                                <span>
                                    <b>FileZero</b>
                                    <em>File Deletion</em>
                                </span>
                            </a>
                            <a href="https://www.isumsoft.com/sysonusb/">
                                <img loading="lazy" src="../images/icon/sysonusb-38.png" width="38" height="38" alt="sysonusb" />
                                <span>
                                    <b>SYSOnUSB</b>
                                    <em>Copy or Install Windows to USB</em>
                                </span>
                            </a>
                            <a href="https://www.isumsoft.com/system-refixer/">
                                <img loading="lazy" src="../images/icon/system-refixer-38.png" width="38" height="38" alt="system refixer" />
                                <span>
                                    <b>System Refixer</b>
                                    <em>Windows Cleanup</em>
                                </span>
                            </a>
                        </div>
                	</div>
                </li>
                <li>iOS & Android<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos i">
                		<div class="hidden">
											<div class="row">
	                			<div class="col-12 col-md-6">
														<a href="https://www.isumsoft.com/iphone-passcode-refixer/">
															<img loading="lazy" src="../images/icon/iphone-passcode-refixer-38.png" width="38" height="38" alt="iphone passcode refixer" />
															<span>
																	<b>iPhone Passcode Refixer</b>
																	<em>iPhone Unlocking</em>
															</span>
														</a>
                            <a href="https://www.isumsoft.com/ilock-refixer/">
															<img loading="lazy" src="../images/icon/ilock-refixer-38.png" width="38" height="38" alt="ilock refixer" />
															<span>
																<b>iLock Refixer</b>
																<em>Unlock iCloud activation lock</em>
															</span>
														</a>
                            <a href="https://www.isumsoft.com/ios-refixer/">
															<img loading="lazy" src="../images/icon/ios-refixer-38.png" width="38" height="38" alt="ios refixer" />
															<span>
																	<b>iOS Refixer</b>
																	<em>iOS System Repair</em>
															</span>
													</a>
                            <a href="https://www.isumsoft.com/backupto/">
                                <img loading="lazy" src="../images/icon/backupto-38.png" width="38" height="38" alt="backupto" />
                                <span>
                                    <b>BackupTo</b>
                                    <em>Back Up iPhone to PC</em>
                                </span>
                            </a>
                            <a href="https://www.isumsoft.com/idevice-cleaner/">
                                <img loading="lazy" src="../images/icon/idevice-cleaner-38.png" width="38" height="38" alt="idevice-cleaner" />
                                <span>
                                    <b>iDevice Cleaner</b>
                                    <em>Clean up iPhone storage</em>
                                </span>
                            </a>
													</div>
													<div class="col-12 col-md-6">
														<a href="https://www.isumsoft.com/android-password-refixer/">
															<img loading="lazy" src="../images/icon/android-38.png" width="38" height="38" alt="android password refixer" />
															<span>
																	<b>Android Password Refixer</b>
																	<em>Bypass Google FRP Lock on Android</em>
															</span>
													</a>
													<a href="https://www.isumsoft.com/android-refixer/">
														<img loading="lazy" src="../images/icon/android-refixer-38.png" width="38" height="38" alt="android refixer" />
														<span>
																<b>Android Refixer</b>
																<em>Android System Repair</em>
														</span>
													</a>
													</div>
												</div>
											</div>
                	</div>
                </li>
                <li>Data Recovery<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos u">
                		<div class="hidden">
                      <div class="row">
															<div class="col-12 col-md-6">
																<a href="https://www.isumsoft.com/office-refixer/">
																	<img loading="lazy" src="../images/icon/office-refixer-38.png" width="38" height="38" alt="office refixer" />
																	<span>
																		<b>Office Refixer</b>
																		<em>Repair corrupted MS Office file</em>
																	</span>
																</a>
																					<a href="https://www.isumsoft.com/word-refixer/">
																	<img loading="lazy" src="../images/icon/word-refixer-38.png" width="38" height="38" alt="word refixer" />
																	<span>
																		<b>Word Refixer</b>
																		<em>Repair corrupted Word file</em>
																	</span>
																</a>
																<a href="https://www.isumsoft.com/excel-refixer/">
																	<img loading="lazy" src="../images/icon/excel-refixer-38.png" width="38" height="38" alt="excel refixer" />
																	<span>
																		<b>Excel Refixer</b>
																		<em>Repair corrupted Excel file</em>
																	</span>
																</a>
																<a href="https://www.isumsoft.com/powerpoint-refixer/">
																	<img loading="lazy" src="../images/icon/powerpoint-refixer-38.png" width="38" height="38" alt="powerpoint refixer" />
																	<span>
																		<b>PowerPoint Refixer</b>
																		<em>Repair corrupted PPT file</em>
																	</span>
																</a>
															</div>
	                						<div class="col-12 col-md-6">
																<a href="https://www.isumsoft.com/data-refixer/">
																	<img loading="lazy" src="../images/icon/data-38.png" width="38" height="38" alt="data refixer" />
																	<span>
																			<b>Data Refixer</b>
																			<em>Recover lost files</em>
																	</span>
															</a>
															<a href="https://www.isumsoft.com/product-key-finder/">
                                <img loading="lazy" src="../images/icon/product-key-finder-38.png" width="38" height="38" alt="product key finder" />
                                <span>
                                    <b>Product Key Finder</b>
                                    <em>Recover software product key</em>
                                </span>
                            </a>
															</div>
											</div>
	                	</div>	
                	</div>
                </li>
                <li>More Tools<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos m">
                		<div class="hidden">
													<a href="https://www.isumsoft.com/bitlocker-reader/">
														<img loading="lazy" src="../images/icon/bitlocker-reader-38.png" width="38" height="38" alt="bitlocker reader" />
														<span>
																<b>BitLocker Reader</b>
																<em>Encrypt drives with BitLocker</em>
														</span>
													</a>
													<a href="https://www.isumsoft.com/bitlocker-reader-for-mac/">
														<img loading="lazy" src="../images/icon/bitlocker-reader-for-mac-38.png" width="38" height="38" alt="bitlocker reader for mac" />
														<span>
																<b>BitLocker Reader for Mac</b>
																<em>Unlock BitLocker Drives on Mac</em>
														</span>
													</a>
													<a href="https://www.isumsoft.com/usbcode/">
														<img loading="lazy" src="../images/icon/usbcode-38.png" width="38" height="38" alt="usbcode" />
														<span>
																<b>USBCode</b>
																<em>Password Protect USB Drive</em>
														</span>
													</a>
													<a href="https://www.isumsoft.com/shapeiso/">
														<img loading="lazy" src="../images/icon/shapeiso-38.png" width="38" height="38" alt="shapeiso" />
														<span>
																<b>ShapeISO</b>
																<em>Burn, Extract and Create ISO</em>
														</span>
													</a>
													<a href="https://www.isumsoft.com/downloads.html">
															<i class="bi bi-grid-fill"></i>
															<span>
																	<b>All Products</b>
															</span>
													</a>
	                	</div>	
                	</div>
                </li>
                <li>Support<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos s">
                		<div class="hidden">
	                		<a href="https://www.isumsoft.com/support/">
	                			<i class="bi bi-person-lines-fill"></i>
	                			<span>
	                				<b>Support Center</b>
	                				<em>FAQs & Technical Support</em>
	                			</span>
	                		</a>
	                		<a href="https://www.isumsoft.com/downloads.html">
	                			<i class="bi bi-cloud-arrow-down-fill"></i>
	                			<span>
	                				<b>Download</b>
	                				<em>Free Download Center</em>
	                			</span>
	                		</a>
	                		<a href="https://www.isumsoft.com/resources/">
	                			<i class="bi bi-chat-square-text-fill"></i>
	                			<span>
	                				<b>Resources</b>
	                				<em>How-to Articles</em>
	                			</span>
	                		</a>
	                		<a href="https://www.isumsoft.com/it/">
	                			<i class="bi bi-brush-fill"></i>
	                			<span>
	                				<b>Blog</b>
	                				<em>iSumsoft IT Blog</em>
	                			</span>
	                		</a>
	                	</div>
                	</div>
                </li>
                <li claclass="nav-li"><a href="https://www.isumsoft.com/store.html"><span class="bi bi-cart4"></span></a></li>
            </ul>
        </div>
    </div>
</div>
<!-- #EndLibraryItem --><div class="bread">
		<div class="crumb"><a href="https://www.isumsoft.com/"><img loading="lazy" src="../images/common/home.png" alt="iSumsoft"
					width="16" height="14" border="0" /></a> <big> »</big> <a href="../unlock-iphone/">Unlock iPhone</a><big>»</big> How to Reset Screen Time Passcode with or without
			Apple ID</div>
	</div>
	<div class="product-main">
		<div class="product-content clearfloat">
			<div class="left">
				<h1>How to Reset Screen Time Passcode with/without Apple ID</h1>
        <div class="author-box">
            <img loading="lazy" src="../images/author/charlotte-bayley.jpg" alt="Charlotte Bayley">
            <div class="author-info">
                <span><a href="../author/charlotte-bayley.html">Charlotte Bayley</a></span>
                <p>Updated: <time>January 11, 2024</time></p>
            </div>
        </div>
				<p><strong>Forgot the Screen Time passcode</strong> on your iPhone or iPad? You don't have to erase or
					factory reset your
					device. With an Apple ID, you can quickly reset your forgotten Screen Time passcode without losing
					any data. This page shows you <strong><a
							href="../unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it.html">how to reset Screen Time
							passcode with or without Apple ID</a></strong> on all iOS versions, including the iOS 17.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/how-to-reset-screen-time-passcode-if-you-forgot-it.webp"
						alt="reset screen time passcode without Apple ID" width="800" height="450" /></p> <br>
				<ul>
					<li><a href="#case1">How to reset Screen Time Passcode with Apple ID (iOS 13.4 or later)</a></li>
					<li><a href="#case2">How to reset Screen Time Passcode without Apple ID (all iOS versions)</a></li>
					<li style="background: none; margin-left: 30px;"><a href="#way1">Way 1: Reset Screen Time Passcode with unlock tool (<b>no data loss</b>)</a></li>
					<li style="background: none; margin-left: 30px;"><a href="#way2">Way 2: Reset Screen Time Passcode with Pinfinder</a></li>
				</ul>
				<h2 id="case1">How to reset Screen Time Passcode with Apple ID (iOS 13.4 or later)</h2>
				<p>iOS does not allow you to change your Screen Time passcode without knowing your current Screne Time
					passcode, unless you have an Apple ID for Screen Time passcode recovery.</p>
				<p>When you set up a Screen Time Passcode on iOS 13.4 or later, you are asked to provide an Apple ID for
					Screen Time Passcode recovery. If the Apple ID was provided, you can use it to reset your forgotten
					Screen Time Password without effort. Of course, the premise is that you still have the email address
					and password for that Apple ID.</p>
				<p><b>Step 1:</b> Open <b>Settings</b>, then tap <b>Screen Time</b> > <b>Change Screen Time Passcode</b> on
					your iPhone.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/navigate-change-passcode.webp"
						alt="tap Change Screen Time Passcode" width="800" height="464" /></p>
				<p><b>Step 2:</b> Select <b>Turn off Screen Time Passcode</b> > <b>Forgot Passcode</b>, then
					enter your Apple ID and password to disable the screen time. </p>
				<p><a href="../unlock-iphone/no-option-for-forgot-screen-time-passcode.html">Related: No Option for Forgot Screen
						Time Passcode?</a></p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/tap-forgot-passcode.webp"
						alt="enter Apple ID to reset Screen Time passcode" width="800" height="464" /></p>

				<h2 id="case2">Reset Screen Time Passcode without Apple ID (all iOS versions)</h2>
				<p>What if you <a href="../unlock-iphone/forgot-apple-id-and-password-to-activate-iphone.html">forgot the Apple ID</a>
					you provided to reset your Screen Time passcode? Or, you forgot your
					Screen Time passcode in iOS 13.3 or earlier? Don't worry! Here are two effective ways for you to
					reset your forgotten Screen Time passcode without an Apple ID. </p>

				<h3 id="way1">Way 1: Reset Screen Time Passcode with unlock tool (no data loss)</h3>
				<p><b><a href="https://www.isumsoft.com/iphone-passcode-refixer/">iSumsoft iPhone Passcode
							Refixer</a></b> helps you easily remove the forgotten Screen Time passcode in any
					iOS version without losing any data, including the latest iOS 17. With a few simple clicks, you can
					reset
					your Screen Time Passcode.</p>
					<div class="product-div">
						<img loading="lazy" src="../images/boxshot/iphone-passcode-refixer.png" alt="iSumsoft iPhone Passcode Refixer" width="140" height="185">
						<div class="function-div">
						  <p class="points">iSumsoft iPhone Passcode Refixer</p>
						  <h2>Remove Various Passcodes on Your iPhone, iPad, and iPod Touch</h2>
						  <ul>
							<li class="points">Unlock Screen Lock from locked, disabled, or unavailable lock screen</li>
							<li>Remove Screen Time passcode or Restrictions passcode without data loss
							</li>
							<li>Delete iCloud account without Apple ID password</li>
							<li>Bypass Remote Management (MDM) Screen & Remove MDM Profile</li>
							<li>Remove iTunes Backup Encryption with one click</li>
							<li>Detect, View, and Export All Passwords with iOS Password Manager</li>
						  </ul>
						  <div class="download-button">
							<a class="download-link win-link"
							  href="https://www.isumsoft.com/download/isumsoft-iphone-passcode-refixer.exe">FREE
							  DOWNLOAD</a>
							<a class="download-link mac-link"
							  href="https://www.isumsoft.com/download/isumsoft-iphone-passcode-refixer.pkg">FREE
							  DOWNLOAD</a>
						  </div>
						</div>
					  </div>  
				<p><b>Step 1:</b> Download and install the <b>iSumsoft iPhone Passcode Refixer</b> program to your PC,
					and then launch it.</p>
				<p><b>Step 2:</b> Connect the iPhone to your PC using USB cable, unlock the iPhone, and then
					tap <b>Trust</b> to trust this PC if prompted.</p>
				<p><b>Step 3:</b> If you have an Apple ID signed in to your iPhone, go to <b>Settings</b> > <b>Apple ID
						Profile</b> >
					<b>Find My</b> > <b>Find
						My iPhone</b> to check if Find My iPhone is turned off. If Find My iPhone is on, turn it off. If
					no
					Apple ID is signed in, Find My iPhone is off.
				</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/turn-off-find-my.webp"
						alt="make sure Find My iPhone is turned off" width="800" height="401" /></p>
				<p><strong>Related:</strong> <a href="../unlock-iphone/turn-off-find-my-iphone-without-password.html">How to Turn Off
						Find My iPhone without Apple ID Password</a></p>
				<p><b>Step 4:</b> From iSumsoft iPhone Passcode Refixer, choose the <b>Remove Screen Time Passcode</b>
					option.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/select-remove-screen-time-passcode.webp"
						alt="choose Remove Screen Time Passcode" width="800" height="604" /></p>
				<p><b>Step 5:</b> On the next page, click the <b>Start</b> button, and the software will automatically
					detect your iPhone
					and start removing the Screen Time passcode from it.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/click-start.webp"
						alt="click Start" width="800" height="604" /></p>
				<p><b>Step 6:</b> Keep the iPhone connected and wait for removing Screen Time passcode to complete. It
					takes only 1
					or 2 minutes. During this process, your iPhone will automatically restart. Don't worry, you won't
					lose any data.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/removing.webp"
						alt="removing Screen Time Passcode" width="800" height="604" /></p>
				<p><b>Step 7:</b> When "Remove Screen Time Passcode Successfully" appears, your iPhone will restart
					automatically without Screen Time passcode.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/click-done.webp"
						alt="Screen Time passcode is removed" width="800" height="604" /></p>
				<p><b>Step 8:</b> Now you can set a new Screen Time Passcode on your iPhone if you like. Just go to
					<b>Settings</b> > <b>Screen Time</b>, then scroll down and tap <b>Use Screen Time Passcode</b>.
					Then enter a 4-digit passcode twice and you've reset your Screen Time passcode.
				</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/set-new-screen-time-passcode.webp"
						alt="set a new Screen Time Passcode" width="800" height="401" /></p>

				<h3 id="way2">Way2: Reset Screen Time Passcode with Pinfinder</h3>
				<p><b>Note:</b> In this section, the required tools (<b>libimobiledevice</b>, <b>iTunes</b>,
					<b>Pinfinder</b>) are stored in the root directory of the D drive, and
					you need to replace the location path with your own when entering the command line.
				</p>
				<p><b>Step 1: </b>Connect device to the computer. If you have never connected the device to the computer
					before, you will be prompted to trust this computer. Tap <b>Trust</b> and enter the screen password
					to
					confirm.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/tap-trust.webp"
						alt="tap-trust" width="800" height="474" /></p>
				<p><b>Step 2: </b>Download <a
						href="https://github.com/libimobiledevice-win32/imobiledevice-net/releases">libimobiledevice
						package</a>, then extract it.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/download-and-extract-libi.webp"
						alt="download-and-extract-libi" width="800" height="478" /></p>
				<p><b>Step 3: </b>Download <a href="https://github.com/gwatts/pinfinder/releases">Pinfinder package</a>
					and then extract it.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/download-and-extract-pinfinder.webp"
						alt="download-and-extract-pinfinder" width="800" height="478" /></p>
				<p><b>Step 4: </b>Download and install <a href="https://www.apple.com/itunes/">iTunes</a> app.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/download-itunes.webp"
						alt="download-itunes" width="800" height="476" /></p>
				<p><b>Step 5: </b>Click the <b>Search</b> icon on the dock, then input "cmd" in the search box and
					select the best match.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/run-cmd.webp"
						alt="run-cmd" width="800" height="731" /></p>
				<p><b>Step 6: </b>Type <span style="background-color: rgb(220, 220, 220);">cd /d
						"D:\libimobiledevice.1.2.1-r1122-win-x64"</span> and hit <b>Enter</b>. </p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/access-libi-package.png"
						alt="access-libi-package" width="800" height="171" /></p>
				<p><b>Step 7: </b>Type <span style="background-color: rgb(220, 220, 220);">idevice_id</span> and hit
					<b>Enter</b> to get iPhone's UUID. (The UUID will be used in the following command lines.)
				</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/get-uuid.png"
						alt="get-uuid" width="800" height="191" /></p>
				<p><b>Step 8: </b>Type <span style="background-color: rgb(220, 220, 220);">idevicebackup2 -h</span> and
					press <b>Enter</b>.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/command-for-help-information.webp"
						alt="command-for-help-information" width="800" height="669" /></p>
				<p><b>Step 9: </b>Enter <span style="background-color: rgb(220, 220, 220);">idevicebackup2 -u
						77872ee1ff47bbfc69ffe1452b9c6ea80fe65051 -i encryption on
						123456</span> and hit Enter, enabling backup encryption. In this command, "123456" is the
					passcode to encrypt the device backup.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/enable-backup-encryption.png"
						alt="enable-backup-encryption" width="800" height="170" /></p>
				<p><b>Step 10: </b>Type <span style="background-color: rgb(220, 220, 220);">idevicebackup2 -u
						77872ee1ff47bbfc69ffe1452b9c6ea80fe65051 backup --full D:\</span> and
					hit <b>Enter</b>, which exports the backup to the D disk.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/move-backup-to-d-drive.png"
						alt="move-backup-to-d-drive" width="800" height="193" /></p>
				<p>The backup file is exported successfully.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/move-backup-successfully.png"
						alt="move-backup-successfully" width="800" height="412" /></p>
				<p><b>Step 11: </b>The backup is stored in the D drive.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/stored-in-drive.png"
						alt="stored-in-drive" width="800" height="378" /></p>
				<p><b>Step 12: </b>Type <span style="background-color: rgb(220, 220, 220);">idevicebackup2 -u
						77872ee1ff47bbfc69ffe1452b9c6ea80fe65051 -i encryption off
						123456</span> and hit <b>Enter</b> to disable backup encryption.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/disable-backup-encryption.png"
						alt="disable-backup-encryption" width="800" height="174" /></p>
				<p><b>Step 13: </b>Type <span style="background-color: rgb(220, 220, 220);">cd /d
						"D:\pinfinder-windows-64bit-1.7.1"</span> and hit <b>Enter</b> to access Pinfinder
					folder.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/access-pinfinder.png"
						alt="access-pinfinder" width="800" height="209" /></p>
				<p><b>Step 14: </b>Type <span style="background-color: rgb(220, 220, 220);">pinfinder
						"D:\77872ee1ff47bbfc69ffe1452b9c6ea80fe65051"</span> and hit <b>Enter</b>.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/decrypt-screen-time-passcode.png"
						alt="decrypt-screen-time-passcode" width="800" height="226" /></p>
				<p><b>Step 15: </b>Enter the backup encryption password set up previously, then hit <b>Enter</b>.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/enter-backup-password.png"
						alt="enter-backup-password" width="800" height="226" /></p>
				<p><b>Step 16: </b>Screen time passcode is displayed in the panel.</p>
				<p><img loading="lazy" src="../images/unlock-iphone/how-to-reset-screen-time-passcode-if-you-forgot-it/screen-time-passcode.png"
						alt="screen-time-passcode" width="800" height="402" /></p>

				<h2>The Final Words:</h2>
				<p>The above is how to reset a Screen Time Passcode on the iPhone with or without an Apple ID. If
					neither method works for you, you may have to erase your iPhone to get rid of the forgotten Screen
					Time passcode. </p>
				<p>See <a
						href="https://www.isumsoft.com/unlock-iphone/how-to-reset-iphone-without-screen-time-passcode.html#method2">How
						to Erase or Reset iPhone without Screen Time Passcode</a>. </p>
				<div class="related-articles clearfloat">
					<h4>Related Articles</h4>
					<ul>
						<li>
							<span>
								<a
									href="https://www.isumsoft.com/it/how-to-change-screen-time-passcode-if-you-forgot-it/">
									<img data-src="../images/blog/change-screen-time-passcode.png"
										alt="Change Screentime Passcode" width="220" height="120">
								</a>
							</span>
							<a href="https://www.isumsoft.com/it/how-to-change-screen-time-passcode-if-you-forgot-it/">How
								to Change Screen Time Passcode if Forgot it</a>
						</li>
						<li>
							<span>
								<a href="../unlock-iphone/forgot-restrictions-passcode-iphone.html">
									<img data-src="../images/unlock-iphone/forgot-restrictions-passcode-iphone/forgot-restrictions-passcode.png"
										alt="Forgot Restrictions Passcode" />
								</a>
							</span>
							<a href="../unlock-iphone/forgot-restrictions-passcode-iphone.html">Forgot Restrictions Passcode - How to
								Reset it</a>
						</li>
						<li>
							<span>
								<a href="../unlock-iphone/reset-forgotten-password-for-apple-id-using-2fa.html">
									<img data-src="../images/unlock-iphone/reset-forgotten-password-for-apple-id-using-2fa/reset-password.png"
										alt="Reset Password for Apple ID" />
								</a>
							</span>
							<a href="../unlock-iphone/reset-forgotten-password-for-apple-id-using-2fa.html">How to Reset Password for
								Apple ID if Forgot it</a>
						</li>
						<li>
							<span>
								<a href="../unlock-iphone/remove-activation-lock-without-previous-owner.html">
									<img data-src="../images/unlock-iphone/remove-activation-lock-without-previous-owner/remove-activation-lock.png"
										alt="remove activation lock without previous owner" />
								</a>
							</span>
							<a href="../unlock-iphone/remove-activation-lock-without-previous-owner.html">How to Remove Activation Lock
								without Previous Owner</a>
						</li>
						<li>
							<span>
								<a href="../unlock-iphone/how-to-remove-apple-id-from-iphone-without-password.html">
									<img data-src="../images/unlock-iphone/how-to-remove-apple-id-from-iphone-without-password/remove-apple-id-without-password.png"
										alt="Remove Apple ID without password" />
								</a>
							</span>
							<a href="../unlock-iphone/how-to-remove-apple-id-from-iphone-without-password.html">How to Remove Apple ID
								from iPhone without Password</a>
						</li>
						<li>
							<span>
								<a href="../unlock-iphone/how-to-remove-parental-control-on-iphone-without-passcode.html">
									<img data-src="../images/unlock-iphone/how-to-remove-parental-control-on-iphone-without-passcode/remove-parental-control-without-passcode.png"
										alt="remove parental control without passcode" />
								</a>
							</span>
							<a href="../unlock-iphone/how-to-remove-parental-control-on-iphone-without-passcode.html">Remove Parental
								Control on iPhone with | without Passcode</a>
						</li>
					</ul>
				</div>
			</div><!-- #BeginLibraryItem "/library/unlock-iphone-sidebar-right.lbi" -->
<div class="sidebar-right">
  <p class="title">Latest Articles</p>
  <ul class="quick-links">
    <li><a href="iphone-unavailable-no-timer.html">iPhone Unavailable No Timer? Unlock the Unavailable Screen Now</a></li>
    <li><a href="iphone-unavailable-black-screen.html">Fix iPhone Unavailable on the Black Screen in White Letters</a></li>
    <li><a href="iphone-unavailable-timer-not-going-down.html">iPhone Unavailable Timer Not Going Down? 6 Fixes!</a></li>
    <li><a href="iphone-unavailable-try-again-in-15-minutes.html">iPhone Unavailable Try Again in 15 Minutes How to Fix</a></li>
    <li><a href="how-to-remove-someone-else-s-apple-id-from-iphone.html">6 Ways to Remove Someone Else’s Apple ID from iPhone [2024]</a></li>
    <li><a href="how-to-bypass-iphone-locked-to-owner.html">How to Bypass iPhone Locked to Owner</a></li>
  </ul>
  <p class="title">Hot Articles</p>
  <ul class="quick-links">
    <li><a href="your-passcode-is-required-to-enable-face-id.html">[Solved] Your Passcode Is Required to Enable Face ID</a></li>
    <li><a href="how-to-erase-iphone-without-passcode.html">How to Erase iPhone without Passcode or Apple ID</a></li>
    <li><a href="how-to-fix-iphone-unavailable.html">iPhone Unavailable on Lock Screen? Solutions to Fix or Unlock [2024]</a></li>
    <li><a href="iphone-unavailable-no-erase-option.html">3 Solutions to Fix iPhone Unavailable but No Erase Option</a></li>
    <li><a href="how-to-unlock-iphone-without-passcode-or-face-id.html">Unlock iPhone X/11/12/13/14/15 without Passcode or Face ID</a></li>
    <li><a href="forgot-iphone-passcode-but-have-fingerprint.html">Solved: Forgot iPhone 7 Passcode But Have Fingerprint</a></li>
  </ul>
</div>
<!-- #EndLibraryItem --></div>
	</div>
	<button class="totop"><span class="bi bi-chevron-up"></span></button><!-- #BeginLibraryItem "/library/footerbox.lbi" -->
<!--cookie-->
<div id="cookie" class="cookie-container">
    <div class="cookie-content">
        <div class="cookie-div">
            <h2>Cookie Settings</h2>
            <p>We use cookies to improve your browsing experience, provide personalized content, and analyze traffic. By clicking 'Accept', you agree to our use of cookies.</p>
        </div>
        <div class="button-div"> 
            <button id="btn-accept" class="cookie-accept cookie-btn">Accept</button>
            <button id="btn-reject" class="cookie-rejuct cookie-btn">Reject</button>
        </div>
    </div>
</div>
<!--footer-->
<div class="footer-box">
  <div class="container">
    <div class="footer-top dis-flex2">
      <div>
        <div><img loading="lazy" src="../images/common/isumsoft2.png" width="239" height="41" alt="logo" /></div>
        <p><a href="mailto:<EMAIL>">Support Team: support#isumsoft.com(Replace # with @)</a><a href="mailto:<EMAIL>">Sales Team: sales#isumsoft.com(Replace # with @)</a></p>
        <ul>
          <li class="title">Follow Us</li>
          <li class="follow-a"> <a href="https://www.facebook.com/iSumsoftOfficial" target="_blank" rel="nofollow"><img loading="lazy" class="follw-icon" src="../images/common/facebook-32.png" alt="facebook" ></a> <a href="https://twitter.com/iSumsoft" target="_blank" rel="nofollow"><img loading="lazy" class="follw-icon" src="../images/common/twitter-32.png" alt="twitter" ></a> <a href="https://www.youtube.com/channel/UCL9RaYqRcQ0f0buIzXiaNDw" target="_blank"><img loading="lazy" class="follw-icon" src="../images/common/youtube-32.png" alt="youtube" ></a> </li>
        </ul>
      </div>
      <div>
        <p class="title">Resources</p>
        <ul>
          <li><a href="https://www.isumsoft.com/unlock-iphone/">Unlock iPhone</a></li>
          <li><a href="https://www.isumsoft.com/iphone-tips/">iPhone Tips</a></li>
          <li><a href="https://www.isumsoft.com/ios-issues/">IOS Issues</a></li>
          <li><a href="https://www.isumsoft.com/mac-tips/">Mac Tips</a></li>
          <li><a href="https://www.isumsoft.com/android/">Android Phone</a></li>
          <li><a href="https://www.isumsoft.com/bitlocker/">Bitlocker</a></li>
          <li><a href="https://www.isumsoft.com/windows-tips/">Windows Tips</a></li>
        </ul>
      </div>
      <div>
        <p class="title">Resources</p>
        <ul>
          <li><a href="https://www.isumsoft.com/windows-password/">Windows Password</a></li>
          <li><a href="https://www.isumsoft.com/backup-recovery/">Data Backup & Recovery</a></li>
          <li><a href="https://www.isumsoft.com/computer-tweaks/">Computer Tweaks</a></li>
          <li><a href="https://www.isumsoft.com/rar-zip/">RAR/ZIP</a></li>
          <li><a href="https://www.isumsoft.com/office/">MS Office</a></li>
          <li><a href="https://www.isumsoft.com/pdf/">PDF Document</a></li>
          <li><a href="https://www.isumsoft.com/internet/">Internet</a></li>
        </ul>
      </div>
      <ul>
        <li class="title">Top Products</li>
        <li><a href="https://www.isumsoft.com/windows-password-refixer/">Windows Password Refixer</a></li>
        <li><a href="https://www.isumsoft.com/iphone-passcode-refixer/">iPhone Passcode Refixer</a></li>
        <li><a href="https://www.isumsoft.com/rar-password-refixer/">RAR Password Refixer</a></li>
        <li><a href="https://www.isumsoft.com/ios-refixer/">iOS Refixer</a></li>
        <li><a href="https://www.isumsoft.com/cloner/">Cloner</a></li>
      </ul>
      <ul>
        <li class="title">Company</li>
        <li><a href="https://www.isumsoft.com/company/">About</a></li>
        <li><a  href="https://www.isumsoft.com/company/contact.html">Contact</a></li>
        <li><a href="https://www.isumsoft.com/company/privacy.html">Privacy</a></li>
        <li><a href="https://www.isumsoft.com/sitemap.html">Sitemap</a></li>
      </ul>
      <ul>
        <li class="title">Help</li>
        <li><a href="https://www.isumsoft.com/it/">Blog</a></li>
        <li><a href="https://www.isumsoft.com/support/technical-faq.html">FAQs</a></li>
        <li><a href="https://www.isumsoft.com/support/">Support</a></li>
      </ul>
    </div>
    <div class="footer-bottom">
      <p>Copyright &copy; 2024 iSumsoft Studio All Rights Reserved.</p>
    </div>
  </div>
</div>
<!-- #EndLibraryItem --><script type="text/javascript" src="../js/nav.js"></script>
	<!-- Default Statcounter code for Isumsoft.com
https://www.isumsoft.com/ -->
	<script type="text/javascript">
		var sc_project = 8760806;
		var sc_invisible = 1;
		var sc_security = "1508d00f"; 
	</script>
	<script type="text/javascript" src="https://www.statcounter.com/counter/counter.js" async></script>
	<noscript>
		<div class="statcounter"><a title="website
statistics" href="https://statcounter.com/" target="_blank"><img loading="lazy" class="statcounter"
					src="https://c.statcounter.com/8760806/0/1508d00f/1/" alt="website statistics" /></a></div>
	</noscript>
	<!-- End of Statcounter Code --><!-- #BeginLibraryItem "/library/share.lbi" -->
	<!-- #EndLibraryItem -->
</body>
</html>