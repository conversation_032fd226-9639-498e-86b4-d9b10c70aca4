
//lazyload
window.addEventListener('load',function (){
	var lazyImages = document.querySelectorAll('img[data-src]'); 
   lazyImages.forEach(function(img){if(img.dataset.src) img.src = img.dataset.src;});
});

// cookies setting
  if (document.getElementById('cookie') != null){
	  initCookieSetting();
  }

//cookie
function hideCookie() {
    document.getElementById('cookie').style.display = 'none';
  }

function initCookieSetting(){
  if(localStorage.getItem('getCookie') === null) {	
	// set default value
	('consent', 'default', {
  	'ad_storage': 'denied',
  	'ad_user_data': 'denied',
  	'ad_personalization': 'denied',
  	'analytics_storage': 'denied'
	});
	  
    document.getElementById('btn-accept').addEventListener('click', function() {
      setCookie({
        marketing: true,
        necessary: true,
        analytics: true,
        preferences: true,
      });
      hideCookie();
    });
    document.getElementById('btn-reject').addEventListener('click', function() {
      setCookie({
        marketing: false,
        necessary: false,
        analytics: false,
        preferences: false,
      });
      hideCookie();
    });
    document.getElementById('cookie').style.display = 'block';
  }
}
function setCookie(agree) {
    const getCookie = {
      'ad_storage': agree.marketing ? 'granted' : 'denied',
      'ad_user_data': agree.necessary ? 'granted' : 'denied',
      'functionality_storage': agree.necessary ? 'granted' : 'denied',
      'analytics_storage': agree.analytics ? 'granted' : 'denied',
      'ad_personalization': agree.preferences ? 'granted' : 'denied',
    };
    ('agree', 'update', getCookie);  
    localStorage.setItem('getCookie', JSON.stringify(getCookie));
  }

//Automatically updated to year
function copyrightFn(){
    document.getElementById('copyright').textContent = new Date().getFullYear();
}
copyrightFn()

window.addEventListener('load',function (){
btnFn('.nav','.nav-app','span');
navFn('.nav li');
goBack('.totop');
faqFn('.change-q-a');
faqTab('.question');
tabFn('#tab-ul li','#tab-f .pro-item');
	//nav
	function btnFn(x,y,z){
		var flag = true;
		var aNav = document.querySelector(x);
		var btn = document.querySelector(y);
		var navSpan = btn.querySelector(z)
		btn.addEventListener('click',function (){
			flag? navboxFn('lines','block',false):navboxFn('','none',true);
		});
		function navboxFn(boxclassname,boxstyle,bl){
			navSpan.className = boxclassname;
			aNav.style.display = boxstyle;
			flag = bl;
		}
	};
	//nav-ul
	 function navFn(x){
		 	var btnLi = document.querySelectorAll(x);
			for(var i=0;i<btnLi.length;i++ ){
				btnLi[i].flag = true;
				btnLi[i].addEventListener('click',function(){
					var aBody = document.body.clientWidth;
					aBody < 1200 && this.flag? boxFn(this,'block','rotate(180deg)',false):boxFn(this,'none','rotate(0deg)',true);
				});
				function boxFn(obj,boxstyle,rote,blon){
					obj.children[1].style.display = boxstyle;
					obj.children[0].style.transform = rote;
					obj.flag = blon;
				}
			};
	};
    
    //TAB Taggle
    function TabFn(){
        var tabLi = document.querySelectorAll('.sys')
        if (!tabLi) return;
        var tabCss = document.querySelectorAll('.button-div')
        function handleChange(num){
            for ( var i =0;i<tabLi.length;i++ ) {
                tabLi[i].className = '';
                tabCss[i].style.display = 'none';
            }
            tabLi[num].className = 'checked';
            tabCss[num].style.display = 'block';

        }
        for ( var i =0;i<tabLi.length;i++ ) {
            tabLi[i].setAttribute('data-index',i);
            tabLi[i].onclick = function (){
                var index = this.getAttribute('data-index');
                handleChange(index)
            }
        }
    }TabFn()
    
    //BTN Taggle
    function BtnFn(){
        var tabSelected = document.querySelectorAll('.sys-btn')
        if (!tabSelected) return;
        var tabDiv = document.querySelectorAll('.sys-div')
        function handleChange(num){
            for ( var i =0;i<tabSelected.length;i++ ) {
                tabSelected[i].className = 'unselected';
                tabDiv[i].style.display = 'none';
            }
            tabSelected[num].className = 'selected';
            tabDiv[num].style.display = 'block';

        }
        for ( var i =0;i<tabSelected.length;i++ ) {
            tabSelected[i].setAttribute('data-index',i);
            tabSelected[i].onclick = function (){
                var index = this.getAttribute('data-index');
                handleChange(index)
            }
        }
    }BtnFn()
	
	//go back top
	function goBack(x){
		var goBack = document.querySelector(x);
		document.addEventListener('scroll', function() {
	        goBack.style.display = window.pageYOffset >= 260? 'block':'none';
	
	    });
		goBack.addEventListener('click', function() {
		    animate(window, 0);
		});
		function animate(obj, target, callback) {
	        clearInterval(obj.timer);
	        obj.timer = setInterval(function() {
	            var step = (target - window.pageYOffset) / 10;
	            step = step > 0 ? Math.ceil(step) : Math.floor(step);
	            if (window.pageYOffset == target) {
	                clearInterval(obj.timer);
	                callback && callback();
	            }
	            window.scroll(0, window.pageYOffset + step);
	        }, 15);
	    }
	};
	
	//faq
	function faqFn(x){
		var changAB = document.querySelectorAll(x);
		for (var i =0; i<changAB.length;i++) {
			changAB[i].onFn = true;
			changAB[i].addEventListener('click',function(){
				this.onFn = this.children[1].style.display == 'block' ?  false:true;
				for( var i =0; i<changAB.length;i++ ){
					changAB[i].children[1].style.display = 'none';
				};
				this.onFn? childreboxFn(this,'block',false):childreboxFn(this,'none',true);
			});
			function childreboxFn(boxdiv,boxstyle,bl){
				boxdiv.children[1].style.display = boxstyle;
				boxdiv.onFn = bl;
			}
		};
	};
	
	//tab
	function tabFn(x,y){
		var tabLi = document.querySelectorAll(x);
		var tabBox = document.querySelectorAll(y);
		//console.log(tabBox.length);
		for ( var i=0; i<tabLi.length; i++ ) {
			tabLi[i].setAttribute('data-index',i);
			tabLi[i].addEventListener('click',function (){
				var index = this.getAttribute('data-index');
				//console.log(index);
				//console.log(tabBox.length);
				for ( var i=0; i<tabLi.length; i++ ) {
					tabLi[i].className = '';
				}
				this.className = 'ul-choose';
				for ( var i =0;i<tabBox.length;i++){
					tabBox[i].style.display = 'none';
				}
				tabBox[index].style.display = 'block';
				
			})
		}
	};

});

//step don't startup with page - chenxin 2024-9-27

function CatFn(){
    var tab = document.querySelector('#step-content');
    if (!tab ) return;
    var tabLi = tab.querySelectorAll('li');
    if (!tabLi) return;
    var tabCss = document.querySelector('#switch-container').querySelectorAll('.img-step');
    function handleChange(num){
        for ( var i =0;i<tabLi.length;i++ ) {
            tabLi[i].className = 'unselected-step';
            tabCss[i].style.display = 'none';
        }
        tabLi[num].className = 'selected-step'
        tabCss[num].style.display = 'block';

    }
    for ( var i =0;i<tabLi.length;i++ ) {
        tabLi[i].setAttribute('data-index',i);
        tabLi[i].onclick = function (){
            var index = this.getAttribute('data-index');
            handleChange(index)
        }
    }
/*    chenxin delete 2024-9-27
aButton.addEventListener('click', function(){
        handleChange(2)
    })
    if (!aMore) return;
    aMore.addEventListener('click', function() {
        handleChange(3)
    })*/
}
    
//faq
function faqTab(x){
    var changAB = document.querySelectorAll(x);
    for (var i =0; i<changAB.length;i++) {
        changAB[i].onFn = true;
        changAB[i].addEventListener('click',function(){
            this.onFn = this.children[1].style.display == 'block' ?  false:true;
            for( var i =0; i<changAB.length;i++ ){
                changAB[i].children[1].style.display = 'none';
            };
            this.onFn? childreboxFn(this,'block',false):childreboxFn(this,'none',true);
        });
        function childreboxFn(boxdiv,boxstyle,bl){
            boxdiv.children[1].style.display = boxstyle;
            boxdiv.onFn = bl;
        }
    };
}
//faq and hot topic
function openTab(evt, tabName) {
  var i, tabcontent, tablinks;
  tabcontent = document.getElementsByClassName("tabcontent");
  for (i = 0; i < tabcontent.length; i++) {
    tabcontent[i].style.display = "none";
  }
  tablinks = document.getElementsByClassName("tab-li");
  for (i = 0; i < tablinks.length; i++) {
    tablinks[i].className = tablinks[i].className.replace(" active", "");
  }
  document.getElementById(tabName).style.display = "block";
  evt.currentTarget.className += " active";
}

// Function to copy content to clipboard
// The script extracts text from <code> elements and joins them with newlines, preserving the code’s structure.
function copyToClipboard(button) {
    // Get the <pre> element next to the button
    const pre = button.nextElementSibling;
    if (!pre || pre.tagName !== 'PRE') {
      console.error('Expected a <pre> element next to the button.');
      button.textContent = 'Error';
      setTimeout(() => button.textContent = 'Copy', 2000);
      return;
    }
  
    // Extract text from all <code> elements within <pre>
    const codeElements = pre.getElementsByTagName('code');
    const codeLines = Array.from(codeElements).map(code => code.textContent.trim());
    const textToCopy = codeLines.join('\n'); // Join lines with newline characters
  
    // Define success and failure handlers
    const onSuccess = () => {
      button.textContent = 'Copied!';
      button.setAttribute('aria-label', 'Content copied to clipboard');
      setTimeout(() => {
        button.textContent = 'Copy';
        button.setAttribute('aria-label', 'Copy to clipboard');
      }, 2000);
    };
  
    const onError = (err) => {
      console.error('Copy operation failed: ', err);
      button.textContent = 'Failed';
      setTimeout(() => button.textContent = 'Copy', 2000);
    };
  
    // Check if Clipboard API is supported
    if (navigator.clipboard && navigator.clipboard.writeText) {
      // Use modern Clipboard API
      navigator.clipboard.writeText(textToCopy).then(onSuccess).catch(onError);
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = textToCopy;
      textArea.style.position = 'fixed'; // Prevent layout disruption
      textArea.style.opacity = '0'; // Hide the textarea
      document.body.appendChild(textArea);
      textArea.select();
      try {
        const successful = document.execCommand('copy');
        if (successful) {
          onSuccess();
        } else {
          onError(new Error('execCommand failed'));
        }
      } catch (err) {
        onError(err);
      }
      document.body.removeChild(textArea);
    }
  }

/* reading process bar */
document.addEventListener('DOMContentLoaded', () => {
  const progressBar = document.getElementById("reading-progress");
  if (!progressBar) return; // If the element doesn't exist, just exit.

  // Detect screen width and height
  const isMobile = window.matchMedia('(max-width: 750px)').matches;
  const pageHeight = document.documentElement.scrollHeight;
  const thresholdHeight = () => window.innerHeight * 3;
  const threshold = thresholdHeight();
  
  if (isMobile || pageHeight < threshold) {
      progressBar.style.display = 'none'; // hide progress bar
      return;
  }

  progressBar.style.display = 'block';
  progressBar.max = 100;
  progressBar.value = 0;
  let timeoutId = null;

  // update progressbar
  const updateProgressBar = () => {
    requestAnimationFrame(() => {
      const winScroll = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
      const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
      const scrolled = height > 0 ? (winScroll / height) * 100 : 0;
      progressBar.value = scrolled;
      progressBar.setAttribute("aria-valuenow", Math.round(scrolled));
    });
  };

  // Stabilization function
  const debounceUpdate = () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(updateProgressBar, 50); // 50ms latency
  };

  // add event listener
  window.addEventListener('scroll', debounceUpdate, { passive: true });
  window.addEventListener('touchmove', debounceUpdate, { passive: true });
  window.addEventListener('resize', updateProgressBar);

  // Initial call, make sure the progress bar is updated when loading
  updateProgressBar();

  // Optional: Clean up event listeners (if you need to dynamically remove the progress bar)
  /*
  return () => {
    window.removeEventListener('scroll', debounceUpdate);
    window.removeEventListener('touchmove', debounceUpdate);
    window.removeEventListener('resize', updateProgressBar);
  };
  */
});

