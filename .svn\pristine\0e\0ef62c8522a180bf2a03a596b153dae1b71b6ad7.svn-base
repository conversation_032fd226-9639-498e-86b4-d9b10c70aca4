<!DOCTYPE html>
<html lang="en">
<head>
<title>3 Ways to Create Windows 10 Bootable USB Drive from ISO</title>
<meta name="Keywords" content="how to create Windows 10 bootable USB drive from ISO" />
<meta name="Description" content="This article shows you how to create a Windows 10 bootable USB flash drive from an ISO image using Rufus or the Command Prompt for installing Windows 10." />
<meta name="copyright" content="iSumsoft" />
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=1" />  
<meta name="format-detection" content="telephone=no" /> 
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />

<link rel="preload" type="font/woff" as="font" href="/fonts/bootstrap-icons.woff" crossorigin/>
<link href="../css/style.css" rel="stylesheet" type="text/css" media="screen" />
<link rel="stylesheet" href="../css/web-app.css" media="(max-width:1024px)" /><!--web-app-style-->

</head>
<body><progress id="reading-progress" value="0" max="100"></progress><!-- #BeginLibraryItem "/library/header.lbi" -->
<div class="header">
    <div class="container">
        <div class="row no-gutters">
            <div class="logo"><a href="https://www.isumsoft.com" title="iSumsoft"><img width="239" height="41" src="https://www.isumsoft.com/images/common/isumsoft.png" alt="iSumsoft Studio" /></a></div>
            <div class="nav-app"><span></span></div>
            <ul class="nav">
                <li>Password Recovery<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos p">
                		<div class="hidden">
	                    <div class="row">
	                        <div class="col-12 col-md-4">
                                <a href="https://www.isumsoft.com/password-refixer-bundle/"><i class="a"></i>Password Refixer Bundle</a>
                                <a href="https://www.isumsoft.com/windows-password-refixer/"><i class="b"></i>Windows Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/windows-7-password-refixer/"><i class="c"></i>Windows 7 Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/office-password-refixer/"><i class="d"></i>Office Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/word-password-refixer/"><i class="e"></i>Word Password Refixer</a>
	                        	
	                        	
	                        </div>
	                        <div class="col-12 col-md-4">
                                <a href="https://www.isumsoft.com/excel-password-refixer/"><i class="f"></i>Excel Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/powerpoint-password-refixer/"><i class="g"></i>PowerPoint Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/rar-password-refixer/"><i class="h"></i>RAR Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/zip-password-refixer/"><i class="i"></i>ZIP Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/pdf-password-refixer/"><i class="j"></i>PDF Password Refixer</a>
	                        	
	                        </div>
                            <div class="col-12 col-md-4">
                                <a href="https://www.isumsoft.com/access-password-refixer/"><i class="k"></i>Access Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/outlook-password-refixer/"><i class="l"></i>Outlook Password Refixer</a>
                                <a href="https://www.isumsoft.com/sql-password-refixer/"><i class="m"></i>SQL Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/wifi-password-refixer/"><i class="n"></i>WiFi Password Refixer</a>
                                <a href="https://www.isumsoft.com/itunes-password-refixer/"><i class="o"></i>iTunes Password Refixer</a>
                            </div>
	                    </div>
	                  </div>
                  </div> 
                </li>
                <li>Backup & Cleanup<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos c">
                		<div class="hidden">
                            <a href="https://www.isumsoft.com/cloner/">
                                <img loading="lazy" src="../images/icon/cloner-38.png" width="38" height="38" alt="cloner" />
                                <span>
                                    <b>Cloner</b>
                                    <em>Disk Cloning</em>
                                </span>
                            </a>
                            <a href="https://www.isumsoft.com/filezero/">
                                <img loading="lazy" src="../images/icon/filezero-38.png" width="38" height="38" alt="filezero" />
                                <span>
                                    <b>FileZero</b>
                                    <em>File Deletion</em>
                                </span>
                            </a>
                            <a href="https://www.isumsoft.com/sysonusb/">
                                <img loading="lazy" src="../images/icon/sysonusb-38.png" width="38" height="38" alt="sysonusb" />
                                <span>
                                    <b>SYSOnUSB</b>
                                    <em>Copy or Install Windows to USB</em>
                                </span>
                            </a>
                            <a href="https://www.isumsoft.com/system-refixer/">
                                <img loading="lazy" src="../images/icon/system-refixer-38.png" width="38" height="38" alt="system refixer" />
                                <span>
                                    <b>System Refixer</b>
                                    <em>Windows Cleanup</em>
                                </span>
                            </a>
                        </div>
                	</div>
                </li>
                <li>iOS & Android<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos i">
                		<div class="hidden">
											<div class="row">
	                			<div class="col-12 col-md-6">
														<a href="https://www.isumsoft.com/iphone-passcode-refixer/">
															<img loading="lazy" src="../images/icon/iphone-passcode-refixer-38.png" width="38" height="38" alt="iphone passcode refixer" />
															<span>
																	<b>iPhone Passcode Refixer</b>
																	<em>iPhone Unlocking</em>
															</span>
														</a>
                            <a href="https://www.isumsoft.com/ilock-refixer/">
															<img loading="lazy" src="../images/icon/ilock-refixer-38.png" width="38" height="38" alt="ilock refixer" />
															<span>
																<b>iLock Refixer</b>
																<em>Unlock iCloud activation lock</em>
															</span>
														</a>
                            <a href="https://www.isumsoft.com/ios-refixer/">
															<img loading="lazy" src="../images/icon/ios-refixer-38.png" width="38" height="38" alt="ios refixer" />
															<span>
																	<b>iOS Refixer</b>
																	<em>iOS System Repair</em>
															</span>
													</a>
                            <a href="https://www.isumsoft.com/backupto/">
                                <img loading="lazy" src="../images/icon/backupto-38.png" width="38" height="38" alt="backupto" />
                                <span>
                                    <b>BackupTo</b>
                                    <em>Back Up iPhone to PC</em>
                                </span>
                            </a>
                            <a href="https://www.isumsoft.com/idevice-cleaner/">
                                <img loading="lazy" src="../images/icon/idevice-cleaner-38.png" width="38" height="38" alt="idevice-cleaner" />
                                <span>
                                    <b>iDevice Cleaner</b>
                                    <em>Clean up iPhone storage</em>
                                </span>
                            </a>
													</div>
													<div class="col-12 col-md-6">
														<a href="https://www.isumsoft.com/android-password-refixer/">
															<img loading="lazy" src="../images/icon/android-38.png" width="38" height="38" alt="android password refixer" />
															<span>
																	<b>Android Password Refixer</b>
																	<em>Bypass Google FRP Lock on Android</em>
															</span>
													</a>
													<a href="https://www.isumsoft.com/android-refixer/">
														<img loading="lazy" src="../images/icon/android-refixer-38.png" width="38" height="38" alt="android refixer" />
														<span>
																<b>Android Refixer</b>
																<em>Android System Repair</em>
														</span>
													</a>
													</div>
												</div>
											</div>
                	</div>
                </li>
                <li>Data Recovery<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos u">
                		<div class="hidden">
                      <div class="row">
															<div class="col-12 col-md-6">
																<a href="https://www.isumsoft.com/office-refixer/">
																	<img loading="lazy" src="../images/icon/office-refixer-38.png" width="38" height="38" alt="office refixer" />
																	<span>
																		<b>Office Refixer</b>
																		<em>Repair corrupted MS Office file</em>
																	</span>
																</a>
																					<a href="https://www.isumsoft.com/word-refixer/">
																	<img loading="lazy" src="../images/icon/word-refixer-38.png" width="38" height="38" alt="word refixer" />
																	<span>
																		<b>Word Refixer</b>
																		<em>Repair corrupted Word file</em>
																	</span>
																</a>
																<a href="https://www.isumsoft.com/excel-refixer/">
																	<img loading="lazy" src="../images/icon/excel-refixer-38.png" width="38" height="38" alt="excel refixer" />
																	<span>
																		<b>Excel Refixer</b>
																		<em>Repair corrupted Excel file</em>
																	</span>
																</a>
																<a href="https://www.isumsoft.com/powerpoint-refixer/">
																	<img loading="lazy" src="../images/icon/powerpoint-refixer-38.png" width="38" height="38" alt="powerpoint refixer" />
																	<span>
																		<b>PowerPoint Refixer</b>
																		<em>Repair corrupted PPT file</em>
																	</span>
																</a>
															</div>
	                						<div class="col-12 col-md-6">
																<a href="https://www.isumsoft.com/data-refixer/">
																	<img loading="lazy" src="../images/icon/data-38.png" width="38" height="38" alt="data refixer" />
																	<span>
																			<b>Data Refixer</b>
																			<em>Recover lost files</em>
																	</span>
															</a>
															<a href="https://www.isumsoft.com/product-key-finder/">
                                <img loading="lazy" src="../images/icon/product-key-finder-38.png" width="38" height="38" alt="product key finder" />
                                <span>
                                    <b>Product Key Finder</b>
                                    <em>Recover software product key</em>
                                </span>
                            </a>
															</div>
											</div>
	                	</div>	
                	</div>
                </li>
                <li>More Tools<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos m">
                		<div class="hidden">
													<a href="https://www.isumsoft.com/bitlocker-reader/">
														<img loading="lazy" src="../images/icon/bitlocker-reader-38.png" width="38" height="38" alt="bitlocker reader" />
														<span>
																<b>BitLocker Reader</b>
																<em>Encrypt drives with BitLocker</em>
														</span>
													</a>
													<a href="https://www.isumsoft.com/bitlocker-reader-for-mac/">
														<img loading="lazy" src="../images/icon/bitlocker-reader-for-mac-38.png" width="38" height="38" alt="bitlocker reader for mac" />
														<span>
																<b>BitLocker Reader for Mac</b>
																<em>Unlock BitLocker Drives on Mac</em>
														</span>
													</a>
													<a href="https://www.isumsoft.com/usbcode/">
														<img loading="lazy" src="../images/icon/usbcode-38.png" width="38" height="38" alt="usbcode" />
														<span>
																<b>USBCode</b>
																<em>Password Protect USB Drive</em>
														</span>
													</a>
													<a href="https://www.isumsoft.com/shapeiso/">
														<img loading="lazy" src="../images/icon/shapeiso-38.png" width="38" height="38" alt="shapeiso" />
														<span>
																<b>ShapeISO</b>
																<em>Burn, Extract and Create ISO</em>
														</span>
													</a>
													<a href="https://www.isumsoft.com/downloads.html">
															<i class="bi bi-grid-fill"></i>
															<span>
																	<b>All Products</b>
															</span>
													</a>
	                	</div>	
                	</div>
                </li>
                <li>Support<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos s">
                		<div class="hidden">
	                		<a href="https://www.isumsoft.com/support/">
	                			<i class="bi bi-person-lines-fill"></i>
	                			<span>
	                				<b>Support Center</b>
	                				<em>FAQs & Technical Support</em>
	                			</span>
	                		</a>
	                		<a href="https://www.isumsoft.com/downloads.html">
	                			<i class="bi bi-cloud-arrow-down-fill"></i>
	                			<span>
	                				<b>Download</b>
	                				<em>Free Download Center</em>
	                			</span>
	                		</a>
	                		<a href="https://www.isumsoft.com/resources/">
	                			<i class="bi bi-chat-square-text-fill"></i>
	                			<span>
	                				<b>Resources</b>
	                				<em>How-to Articles</em>
	                			</span>
	                		</a>
	                		<a href="https://www.isumsoft.com/it/">
	                			<i class="bi bi-brush-fill"></i>
	                			<span>
	                				<b>Blog</b>
	                				<em>iSumsoft IT Blog</em>
	                			</span>
	                		</a>
	                	</div>
                	</div>
                </li>
                <li claclass="nav-li"><a href="https://www.isumsoft.com/store.html"><span class="bi bi-cart4"></span></a></li>
            </ul>
        </div>
    </div>
</div>
<!-- #EndLibraryItem --><div class="bread"><div class="crumb"><a href="https://www.isumsoft.com/"><img loading="lazy" src="../images/common/home.png" alt="iSumsoft" width="16" height="14" border="0" /></a> <big> »</big><a href="../computer-tweaks/">Computer Tweaks</a><big>»</big>How to Create a Windows 10 Bootable USB Flash Drive from ISO</div>
</div>
<div class="product-main">
  <div class="product-content">
    <div class="left">
        <h1>How to Create a Windows 10 Bootable USB Flash Drive from ISO</h1>
	  <div class="author-box">
		<img loading="lazy" src="../images/author/isabella-shinn.jpg" alt="Isabella Shinn">
		<div class="author-info">
			<span><a href="../author/isabella-shinn.html">Isabella Shinn</a></span>
			<p>Updated: <time>January 11, 2024</time></p>
		</div>
	  </div>
      <p>To create a bootable USB drive for installing Windows 10, the preferred method is of course using the Windows 10 Media Creation Tool. But what if the Media Creation Tool doesn't work for you, or you don't like using it? Don't worry, you can also <a href="create-windows-10-bootable-usb-from-iso.html">create a Windows 10 bootable USB drive from an ISO image</a> file without using the Media Creation Tool.</p>
      <p>This article will show you how to create a Windows 10 bootable USB drive from ISO for installing Windows 10. Make sure you have downloaded a <a href="../windows-tips/how-to-download-windows-10-iso-from-microsoft.html">Windows 10 ISO</a> file onto your computer, then follow the methods and steps below.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/create-windows-10-bootable-usb-from-iso.webp" alt="create Windows 10 bootable USB from ISO" width="700" height="383" /></p>
      <br/>
      <ul class="guide-ul">
        <li><a href="#rufus">Way 1: Create Windows 10 bootable USB from ISO using Rufus</a></li>
        <li><a href="#shapeiso">Way 2: Create Windows 10 Bootable USB from ISO using ShapeISO</a></li>
        <li><a href="#cmd">Way 3: Create Windows 10 bootable USB from ISO using CMD</a></li>
      </ul>
      <h2 id="rufus">Way 1: Create Windows 10 bootable USB from ISO using Rufus</h2>
      <p>Rufus is an open-source application for easily creating bootable USB flash drives or <a href="create-windows-to-go-usb-drive-with-rufus.html">Windows To Go</a> live USB drives. Here is how to easily create a Windows 10 bootable USB flash drive from an ISO image using Rufus.</p>
      <p>1. Plug a USB flash drive into your computer and back up the data on the USB drive (if any) to another place.</p>
      <p>2. Download the <a href="https://github.com/pbatard/rufus/releases/download/v3.18/rufus-3.18.exe">Rufus</a> program onto your computer, and then run it directly without installation.</p>
      <p>3. On Rufus, set the following options to create a Windows 10 bootable USB flash drive. </p>
      <ul>
        <li>Select the USB flash drive from the Device drop-down menu.</li>
        <li>Click the SELECT button to import the Windows 10 ISO file.</li>
        <li>Select Standard Windows installation from the "image option" dropdown.</li>
        <li>Leave the other options at their default values and click Start.</li>
        <li>Click OK and Rufus will start creating the Windows 10 bootable USB flash drive.</li>
      </ul>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/create-bootable-usb-from-iso-rufus.png" alt="create Windows 10 bootable USB from ISO using Rufus" width="700" height="454" /></p>
      <p>4. Just wait for the creation to complete. This usually takes 15 to 30 minutes.</p>
      <p>5. When the progress bar goes to 100% and displays Ready, it indicates that Rufus has successfully created a Windows 10 bootable USB drive from the iSO.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/windows-10-bootable-usb-ready.png" alt="Windows 10 bootable USB drive is ready" width="700" height="454" /></p>
      <h2 id="shapeiso">Way 2: Create Windows 10 bootable USB from ISO using ShapeISO</h2>
      <p><a href="../shapeiso/">iSumsoft ShapeISO</a> is another powerful and yet easy-to-use tool that can help you easily create a Windows 10 bootable USB drive from an ISO image file.</p>
      <p>Step 1: Download and install iSumsoft ShapeISO on your PC. After installation, launch this tool.</p>
      <p>Step 2: Select the Burn option, click the file icon to import the Windows 10 ISO file, click the Refresh icon to show your connected USB drive, and then click the Start button. The tool will immediately start burning the Windows 10 ISO file to the USB drive to create a bootable Windows 10 USB drive.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/use-isumsoft-shapeiso.png" alt="use iSumsoft ShapeISO" width="700" height="477" /></p>
      <p>Step 3: Wait until the process is complete. When the software page shows that the   ISO image has been successfully burned to the USB drive, the Windows 10 bootable USB is created. </p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/creation-complete.png" alt="Windows 10 bootable USB created" width="700" height="477" /></p>
      <h2 id="cmd">Way 3: Create Windows 10 bootable USB from ISO using CMD</h2>
      <p>If you want to create a Windows 10 bootable USB drive without using any software, the Windows Command Prompt can do it for you. Here is how to create a Windows 10 bootable USB flash drive from an ISO image using Command Prompt.</p>
      <h3>Step 1: Reformat the USB flash drive.</h3>
      <p>1. Connect the USB flash drive into your computer and back up the data on the USB drive (if any) to another place.</p>
      <p>2. Type cmd in your Windows search box. When the Command Prompt app  shortcut appears in the search results, click Run as administrator. </p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/run-command-prompt-as-administrator.png" alt="run Command Prompt as administrator" width="700" height="439" /></p>
      <p>3. In the Command Prompt window, type <strong>diskpart</strong> and press Enter to access the diskpart tool.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/run-diskpart.png" alt="run diskpart" width="700" height="242" /></p>
      <p>4. Type<strong> list disk </strong>and press Enter to list all disks connected to your computer. Then judge which is your USB flash drive according to the disk size.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/list-disk.png" alt="type list disk" width="700" height="241" /></p>
      <p>5. Type <strong>select disk #</strong> and press Enter to select your USB flash drive. "<strong>#</strong>" is the disk number of your USB flash drive.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/select-disk.png" alt="select disk" width="700" height="241" /></p>
      <p>6. Type <strong>clean</strong> and press Enter to erase all partitions as well as data on your USB flash drive.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/type-clean.png" alt="type clean" width="700" height="241" /></p>
      <p>7. Type <strong>convert mbr</strong> and press Enter to convert the USB flash drive to MBR format.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/type-convert-mbr.png" alt="type convert mbr" width="700" height="241" /></p>
      <p>8. Type <strong>create partition primary</strong> and press Enter to create a primary partition on the USB flash drive.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/type-create-partition-primary.png" alt="type create partition primary" width="700" height="241" ></p>
      <p>9. Type <strong>active</strong> and press Enter to active the primary partition on the USB flash drive.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/type-active.png" alt="type active" width="700" height="241" /></p>
      <p>10. Type <strong>format quick fs=fat32 label="SWM-USB"</strong> and press Enter to quickly format the primary partition as FAT32 and set its volume label as SWM-USB.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/format-usb-as-fat32.png" alt="format USB drive as FAT32" width="700" height="241" /></p>
      <p>11. Type <strong>assign letter="X"</strong> and press Enter to set the drive letter of the partition to X.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/assign-letter-for-usb.png" alt="assign letter for USB drive" width="700" height="241" /></p>
      <p>12. Type <strong>list volume</strong> and press Enter to list all volumes on your PC so that you can view the volume details of your USB  drive.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/list-volume.png" alt="list volume" width="700" height="241" /></p>
      <p>13. Type <strong>exit</strong> and press Enter to exit the diskpart tool, leave the Command Prompt window there or close it.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/exit-diskpart.png" alt="exit diskpart" width="700" height="241" /></p>
      <h2>Step 2: Create Windows 10 bootable USB drive from ISO.</h2>
      <p>1. Locate the downloaded Windows 10 ISO file and double-click it to mount it to your computer. </p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/mount-iso-image.png" alt="mount Windows ISO image" width="700" height="272" /></p>
      <p>2. Windows will automatically assign a DVD drive letter for the mounted ISO image. Write down the DVD drive letter (E: in my case). </p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/iso-image-drive-letter.png" alt="DVD drive letter of the ISO" width="700" height="337" /></p>
      <p>3. Go back to the previously Command Prompt window or open a new Command Prompt window as administrator. Then, type <strong>robocopy E:\ X:\ /E /xf install.wim</strong> and press Enter to copy all files except the install.wim file in the mounted Windows 10 ISO image to the root directory of the USB flash drive. Then, wait for the file copy to complete. This takes about three minutes. </p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/copying-files.png" alt="copying files" width="700" height="262" /></p>
      <p>4. All files except the install.wim file in the ISO image have been successfully copied to the root directly of the USB flash drive.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/files-copied.png" alt="files copied" width="700" height="261" /></p>
      <p>5. In this step, you will copy the install.wim file from the ISO image to the USB flash drive. Since the install.wim file is larger than 4GB and the FAT32 formatted USB drive cannot store a single file over 4GB, you need to divide the install.wim file into two install.swm files and save them on the USB flash drive. To do that, type the following dism command and press Enter.</p>
      <p><strong>Dism /Split-Image /ImageFile:E:\sources\install.wim /SWMFile:X:\sources\install.swm /FileSize:2500</strong></p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/split-image.png" alt="split image" width="700" height="239" /></p>
      <p>6. A few minutes later, the install.swm files are successfully split from the install.wim file and stored in the sources folder in the USB flash drive.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/operation-completed.png" alt="operation completed" width="700" height="239" /></p>
      <p>7. Now go to the USB flash drive, open the sources folder, and you will see two install.swm image files.</p>
      <p><img loading="lazy" src="../images/computer-tweaks/create-windows-10-bootable-usb-from-iso/image-copied.png" alt="image copied to USB drive" width="700" height="319" /></p>
      <p>That's all the steps required to create a Windows 10 bootable USB flash drive from an ISO image using Command Prompt. Then, boot the computer from the USB flash drive and you can install Windows 10 on the computer.</p>
      <h3>Conclusion:</h3>
      <p>The above how to create a Windows 10 bootable USB drive from an ISO image file for installing Windows 10. Both Rufus and iSumsoft ShapeISO are good options, they are safe, effective and easy to use. The Command Prompt allows you to successfully create a Windows 10 bootable USB drive from an ISO image without using any software, but the process is cumbersome. You need to make sure you run the correct commands to make the USB drive bootable. If you are not good at using Windows commands, Rufus or iSumsoft ShapeISO is still your best choice.</p>
      <p>You may also be interested in <a href="make-external-hard-drive-bootable-windows-10.html">How to Make External Hard Drive Bootable for Windows 10</a>.</p>
      <div class="related-articles clearfloat">
        <h4>Related Articles</h4>
        <ul>
         <li> 
        <span><a href="../windows-tips/how-to-extract-iso-file-in-windows-10.html"><img data-src="../images/windows-tips/how-to-extract-iso-file-in-windows-10/3-ways-to-extract-iso-file-in-windows-10.webp" alt="3 Ways to Open and Extract ISO File in Windows 10" width="220" height="120" /></a></span>
        <a href="../windows-tips/how-to-extract-iso-file-in-windows-10.html"> How to Extract ISO File in Windows 10</a></li>
        
        <li> 
        <span><a href="https://www.isumsoft.com/computer-tweaks/windows-10-bootable-usb-not-working.html"><img data-src="../images/computer-tweaks/windows-10-bootable-usb-not-working/how-to-fix-windows-10-bootable-usb-not-working.webp" alt=" 4 Ways to fix Windows 10 bootable USB Not Working" width="220" height="120" /></a></span>
        <a href="https://www.isumsoft.com/computer-tweaks/windows-10-bootable-usb-not-working.html">How to Fix Windows 10 Bootable USB Not Working</a></li>
         <li> 
        <span><a href="https://www.isumsoft.com/computer-tweaks/how-to-check-if-a-usb-drive-is-bootable-in-windows-10.html"><img data-src="../images/computer-tweaks/how-to-check-if-a-usb-drive-is-bootable-in-windows-10/how-to-check-if-a-usb-drive-is-bootable-in-windows-10.webp" alt=" how to check if a USB drive is bootable in Windows 10" width="220" height="120"/></a></span>
        <a href="https://www.isumsoft.com/computer-tweaks/how-to-check-if-a-usb-drive-is-bootable-in-windows-10.html">How  to Check If A USB Drive is Bootable in Windows 10</a></li>
<li>
	<span>
		<a href="create-windows-to-go-usb-drive-in-windows-10-enterprise.html">
			<img data-src="../images/computer-tweaks/create-windows-to-go-usb-drive-in-windows-10-enterprise/create-windows-to-go-in-windows-10-enterprise.png" alt="create Windows To Go in Windows 10 Enterprise"/>
		</a>
	</span>
	<a href="create-windows-to-go-usb-drive-in-windows-10-enterprise.html">How to Create Windows to Go USB in Windows 10 Enterprise</a>
</li>
<li>
	<span>
		<a href="../backup-recovery/how-to-copy-windows-10-to-usb-and-make-it-bootable.html">
			<img data-src="../images/backup-recovery/how-to-copy-windows-10-to-usb-and-make-it-bootable/copy-existing-windows-10-to-usb.webp" alt="copy Windows 10 to to USB"/>
		</a>
	</span>
	<a href="../backup-recovery/how-to-copy-windows-10-to-usb-and-make-it-bootable.html">How to Copy Windows 10 to USB Drive and Make It Bootable</a>
</li>
<li>
	<span>
		<a href="../backup-recovery/how-to-copy-c-drive-to-external-hard-drive.html">
			<img data-src="../images/backup-recovery/how-to-copy-c-drive-to-external-hard-drive/copy-c-drive-to-external-hard-drive.png" alt="copy C drive to external hard drive"/>
		</a>
	</span>
	<a href="../backup-recovery/how-to-copy-c-drive-to-external-hard-drive.html">How to Copy C Drive to External Hard Drive in Windows 10</a>
</li>
<li>
	<span>
		<a href="../windows-tips/media-creation-tool-not-working.html">
			<img data-src="../images/windows-tips/media-creation-tool-not-working/how-to-fix-media-creation-tool-not-working.webp" alt="how to fix Media Creation Tool not working"/>
		</a>
	</span>
	<a href="../windows-tips/media-creation-tool-not-working.html">[FIXED] Media Creation Tool Not Working in Windows 10</a>
</li>
<li>
	<span>
		<a href="how-to-password-protect-external-hard-drive.html">
			<img data-src="../images/computer-tweaks/how-to-password-protect-external-hard-drive/password-protect-external-hard-drive.png" alt="password protect external hard drive"/>
		</a>
	</span>
	<a href="how-to-password-protect-external-hard-drive.html">How to Password Protect External Hard Drive in Windows 10</a>
</li>
<li>
	<span>
		<a href="../backup-recovery/install-windows-10-on-new-ssd.html">
			<img data-src="../images/backup-recovery/install-windows-10-on-new-ssd/install-windows-10-on-ssd.png" alt="install Windows 10 on new SSD"/>
		</a>
	</span>
	<a href="../backup-recovery/install-windows-10-on-new-ssd.html">Fress Install Windows 10 on New SSD without Removing Old HDD</a>
</li>
        </ul>
      </div>
    </div><!-- #BeginLibraryItem "/library/computer-tweaks-sidebar-right.lbi" -->
<div class="sidebar-right">
  <p class="title">Latest Articles</p>
  <ul class="quick-links">
        <li><a href="how-to-display-the-real-time-hardware-status-on-your-screen-via-msi-afterburner.html">How to Display Hardware Status on Screen via MSI Afterburner </a></li>
        <li><a href="windows-10-11-laptop-maintenance-hardware-and-software.html">Windows 10/11 Laptop Maintenance: Hardware and Software</a></li>
        <li><a href="check-laptop-battery-health-condition-on-windows-10-11.html">Check Laptop Battery Health Condition on Windows 10/11</a></li>
        <li><a href="see-number-of-cpu-core-and-processor-your-pc-has.html">See How Many CPU Cores Your Processor Has</a></li>
        <li><a href="make-external-hard-drive-bootable-windows-10.html">3 Ways to Make External Hard Drive Bootable for Windows 10</a></li>
        <li><a href="how-to-unlock-your-laptop-when-keyboard-not-working.html">4 Methods to Unlock Your Laptop When Keyboard Not Working </a></li>
  </ul>
  <p class="title">Hot Articles</p>
  <ul class="quick-links">
        <li><a href="how-to-unlock-your-laptop-when-keyboard-not-working.html">4 Methods to Unlock Your Laptop When Keyboard Not Working </a></li>
        <li><a href="check-and-fix-drive-errors-in-windows-10.html">5 Quick Tips for Checking and Fixing Hard Drive Errors</a></li>
        <li><a href="windows-11-how-to-create-a-system-image.html">How to Create a System Image in Windows 11 </a></li>
        <li><a href="make-external-hard-drive-bootable-windows-10.html">How to Make External Hard Drive Bootable in Windows 10</a></li>
        <li><a href="how-to-display-the-real-time-hardware-status-on-your-screen-via-msi-afterburner.html">How to Display Hardware Status on Screen via MSI Afterburner </a></li>
        <li><a href="convert-bootable-usb-to-iso-windows-10.html">2 Free Ways to Convert Bootable USB to ISO on Windows 10</a></li>
  </ul>
</div>
<!-- #EndLibraryItem --></div>
</div>
<div class="clear"></div>
<button class="totop"><span class="bi bi-chevron-up"></span></button><!-- #BeginLibraryItem "/library/footerbox.lbi" -->
<!--cookie-->
<div id="cookie" class="cookie-container">
    <div class="cookie-content">
        <div class="cookie-div">
            <h2>Cookie Settings</h2>
            <p>We use cookies to improve your browsing experience, provide personalized content, and analyze traffic. By clicking 'Accept', you agree to our use of cookies.</p>
        </div>
        <div class="button-div"> 
            <button id="btn-accept" class="cookie-accept cookie-btn">Accept</button>
            <button id="btn-reject" class="cookie-rejuct cookie-btn">Reject</button>
        </div>
    </div>
</div>
<!--footer-->
<div class="footer-box">
  <div class="container">
    <div class="footer-top dis-flex2">
      <div>
        <div><img loading="lazy" src="../images/common/isumsoft2.png" width="239" height="41" alt="logo" /></div>
        <p><a href="mailto:<EMAIL>">Support Team: support#isumsoft.com(Replace # with @)</a><a href="mailto:<EMAIL>">Sales Team: sales#isumsoft.com(Replace # with @)</a></p>
        <ul>
          <li class="title">Follow Us</li>
          <li class="follow-a"> <a href="https://www.facebook.com/iSumsoftOfficial" target="_blank" rel="nofollow"><img loading="lazy" class="follw-icon" src="../images/common/facebook-32.png" alt="facebook" ></a> <a href="https://x.com/iSumsoft" target="_blank" rel="nofollow"><img loading="lazy" class="follw-icon" src="../images/common/twitter-32.png" alt="twitter" ></a> <a href="https://www.youtube.com/channel/UCL9RaYqRcQ0f0buIzXiaNDw" target="_blank"><img loading="lazy" class="follw-icon" src="../images/common/youtube-32.png" alt="youtube" ></a> </li>
        </ul>
      </div>
      <div>
        <p class="title">Resources</p>
        <ul>
          <li><a href="https://www.isumsoft.com/unlock-iphone/">Unlock iPhone</a></li>
          <li><a href="https://www.isumsoft.com/iphone-tips/">iPhone Tips</a></li>
          <li><a href="https://www.isumsoft.com/ios-issues/">IOS Issues</a></li>
          <li><a href="https://www.isumsoft.com/mac-tips/">Mac Tips</a></li>
          <li><a href="https://www.isumsoft.com/android/">Android Phone</a></li>
          <li><a href="https://www.isumsoft.com/bitlocker/">Bitlocker</a></li>
          <li><a href="https://www.isumsoft.com/windows-tips/">Windows Tips</a></li>
        </ul>
      </div>
      <div>
        <p class="title">Resources</p>
        <ul>
          <li><a href="https://www.isumsoft.com/windows-password/">Windows Password</a></li>
          <li><a href="https://www.isumsoft.com/backup-recovery/">Data Backup & Recovery</a></li>
          <li><a href="https://www.isumsoft.com/computer-tweaks/">Computer Tweaks</a></li>
          <li><a href="https://www.isumsoft.com/rar-zip/">RAR/ZIP</a></li>
          <li><a href="https://www.isumsoft.com/office/">MS Office</a></li>
          <li><a href="https://www.isumsoft.com/pdf/">PDF Document</a></li>
          <li><a href="https://www.isumsoft.com/internet/">Internet</a></li>
        </ul>
      </div>
      <ul>
        <li class="title">Top Products</li>
        <li><a href="https://www.isumsoft.com/windows-password-refixer/">Windows Password Refixer</a></li>
        <li><a href="https://www.isumsoft.com/iphone-passcode-refixer/">iPhone Passcode Refixer</a></li>
        <li><a href="https://www.isumsoft.com/rar-password-refixer/">RAR Password Refixer</a></li>
        <li><a href="https://www.isumsoft.com/ios-refixer/">iOS Refixer</a></li>
        <li><a href="https://www.isumsoft.com/cloner/">Cloner</a></li>
      </ul>
      <ul>
        <li class="title">Company</li>
        <li><a href="https://www.isumsoft.com/company/">About</a></li>
        <li><a  href="https://www.isumsoft.com/company/contact.html">Contact</a></li>
        <li><a href="https://www.isumsoft.com/company/privacy.html">Privacy</a></li>
        <li><a href="https://www.isumsoft.com/sitemap.html">Sitemap</a></li>
      </ul>
      <ul>
        <li class="title">Help</li>
        <li><a href="https://www.isumsoft.com/it/">Blog</a></li>
        <li><a href="https://www.isumsoft.com/support/technical-faq.html">FAQs</a></li>
        <li><a href="https://www.isumsoft.com/support/">Support</a></li>
      </ul>
    </div>
    <div class="footer-bottom">
      <p>Copyright&nbsp;&copy;&nbsp;<span id="copyright"><noscript>2025</noscript></span>&nbsp;iSumsoft Studio All Rights Reserved.</p>
    </div>
  </div>
</div>
<!-- #EndLibraryItem --><script type="text/javascript" src="../js/nav.js" ></script>
<!-- Default Statcounter code for Isumsoft.com
https://www.isumsoft.com/ -->
<script type="text/javascript">
var sc_project=8760806; 
var sc_invisible=1; 
var sc_security="1508d00f"; 
</script>
<script type="text/javascript"
src="https://www.statcounter.com/counter/counter.js"
async></script>
<noscript><div class="statcounter"><a title="website
statistics" href="https://statcounter.com/"
target="_blank"><img loading="lazy" class="statcounter"
src="https://c.statcounter.com/8760806/0/1508d00f/1/"
alt="website statistics" /></a></div></noscript>
<!-- End of Statcounter Code --><!-- #BeginLibraryItem "/library/share.lbi" -->
<!-- #EndLibraryItem --></body>
</html>